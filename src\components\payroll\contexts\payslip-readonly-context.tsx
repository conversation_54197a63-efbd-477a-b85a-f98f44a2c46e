"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { usePayslip } from "@/hooks/tanstack-query/usePayslip";

interface PayslipReadOnlyContextType {
  isReadOnly: boolean;
  payslipStatus: string | null;
  isLoading: boolean;
}

const PayslipReadOnlyContext = createContext<
  PayslipReadOnlyContextType | undefined
>(undefined);

interface PayslipReadOnlyProviderProps {
  children: ReactNode;
  employeeId: string | null;
  periodId: string;
}

/**
 * Context provider that manages read-only state for payslips based on their status.
 * When a payslip has "closed" status, all UI components should be disabled.
 */
export const PayslipReadOnlyProvider: React.FC<
  PayslipReadOnlyProviderProps
> = ({ children, employeeId, periodId }) => {
  // Only fetch payslip data if we have a valid employeeId
  const shouldFetch = !!employeeId && !!periodId;
  const { data, isLoading } = usePayslip(employeeId || "", periodId);

  // Determine if the payslip is in read-only mode
  // If we don't have an employeeId, default to not read-only
  const payslipStatus = shouldFetch ? data?.payslip?.status || null : null;
  const isReadOnly = shouldFetch && payslipStatus === "closed";

  const contextValue: PayslipReadOnlyContextType = {
    isReadOnly,
    payslipStatus,
    isLoading: shouldFetch ? isLoading : false,
  };

  return (
    <PayslipReadOnlyContext.Provider value={contextValue}>
      {children}
    </PayslipReadOnlyContext.Provider>
  );
};

/**
 * Hook to access the payslip read-only context.
 * Returns the current read-only state and payslip status.
 */
export const usePayslipReadOnly = (): PayslipReadOnlyContextType => {
  const context = useContext(PayslipReadOnlyContext);
  if (context === undefined) {
    throw new Error(
      "usePayslipReadOnly must be used within a PayslipReadOnlyProvider",
    );
  }
  return context;
};

/**
 * Hook that provides a simple boolean for whether components should be disabled.
 * This is a convenience hook for components that only need to know if they should be disabled.
 * Returns false if used outside of PayslipReadOnlyProvider context.
 */
export const useIsPayslipReadOnly = (): boolean => {
  try {
    const { isReadOnly } = usePayslipReadOnly();
    return isReadOnly;
  } catch (error) {
    // If used outside of PayslipReadOnlyProvider, default to not read-only
    return false;
  }
};
