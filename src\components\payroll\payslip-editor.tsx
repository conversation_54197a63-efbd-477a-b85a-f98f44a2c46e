"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";

import React, { useState, useEffect, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { useEmployeesForPeriod } from "@/hooks/tanstack-query/useEmployeesForPeriod";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Calendar, Clock, Plus, Save, LayoutGrid, Edit } from "lucide-react";

// Import period data and payslip section components

import PayslipAdditionsSection from "@/components/payroll/payslip-sections/additions-section";
import PayslipDeductionsSection from "@/components/payroll/payslip-sections/deductions-section";
import PayslipStatutorySection from "@/components/payroll/payslip-sections/statutory-section";
import PayslipPensionSection from "@/components/payroll/payslip-sections/pension-section";
import PayslipNotesSection from "@/components/payroll/payslip-sections/notes-section";
import PayslipSummary from "@/components/payroll/payslip-sections/summary-section";
import EmployeeList from "@/components/payroll/employee-list";
import { PayslipBasicSectionContainer } from "@/components/payroll/payslip-sections/payslip-basic-section-container";
import { PayslipReadOnlyProvider } from "@/components/payroll/contexts/payslip-readonly-context";
import { ReadOnlyIndicator } from "@/components/payroll/ui/readonly-aware-components";

// Mock data for a payslip - will be replaced with actual data from database
const mockPayslip = {
  id: "ps-123",
  employeeId: "2",
  periodId: "march-2025",
  status: "open",
  basic: {
    elements: [
      {
        id: "salary-1",
        type: "salary", // Removed 'as const' to comply with linter
        amount: 3200,
        periodType: "Monthly",
        isRepeating: true,
        zeroizeNext: false,
      },
      {
        id: "hourly-1",
        type: "hourly", // Removed 'as const' to comply with linter
        rate: 20,
        hours: 160,
        isRepeating: false,
        zeroizeNext: false,
        amount: 20 * 160, // Pre-calculate amount for type compatibility
      },
      {
        id: "daily-1",
        type: "daily", // Removed 'as const' to comply with linter
        rate: 160,
        days: 20,
        isRepeating: false,
        zeroizeNext: false,
        amount: 160 * 20, // Pre-calculate amount for type compatibility
      },
    ],
  },
  additions: {
    items: [
      {
        id: "add-1",
        type: "bonus",
        name: "Bonus",
        amount: 0,
        isRepeating: false,
        zeroizeNext: false,
      },
      {
        id: "add-2",
        type: "commission",
        name: "Commission",
        amount: 0,
        isRepeating: true,
        zeroizeNext: true,
      },
    ],
  },
  deductions: {
    items: [
      {
        id: "ded-1",
        type: "advance",
        name: "Advance",
        amount: 0,
        isRepeating: false,
        zeroizeNext: false,
      },
      {
        id: "ded-2",
        type: "salary-sacrifice",
        name: "Salary Sacrifice",
        amount: 0,
        isRepeating: true,
        zeroizeNext: false,
      },
    ],
  },
  statutory: {
    ssp: 0,
    smp: 0,
    spp: 0,
    shpp: 0,
  },
  pension: {
    employeeContribution: 160,
    employerContribution: 320,
    isPercentage: true,
    employeePercentage: 5,
    employerPercentage: 10,
  },
  notes: [
    {
      id: "note-1",
      content: "Employee requested tax code check",
      showOnPayslip: true,
      isRepeating: false,
    },
  ],
  calculations: {
    taxablePay: 3200,
    tax: 573.2,
    studentLoan: 0,
    postgraduateLoan: 0,
    employeeNIC: 284.16,
    employerNIC: 364.16,
    netPay: 2182.64,
    additionsToNetPay: 0,
    deductionsFromNetPay: 0,
    employeePension: 160,
    employerPension: 320,
    takeHomePay: 2182.64,
    employerCost: 3884.16,
  },
  ytd: {
    gross: 9600,
    taxablePay: 9600,
    tax: 1719.6,
    nationalInsurance: 852.48,
    employeePension: 480,
    employerPension: 960,
    netPay: 6547.92,
  },
};

import type { PayPeriod } from "@/drizzle/schema/employer/payPeriod";

// Utility function to get ordinal suffix for a number
const getOrdinalSuffix = (day: number): string => {
  if (day >= 11 && day <= 13) {
    return "th";
  }
  switch (day % 10) {
    case 1:
      return "st";
    case 2:
      return "nd";
    case 3:
      return "rd";
    default:
      return "th";
  }
};

// Utility function to format date with ordinal suffix
const formatDateWithOrdinal = (dateString: string): string => {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleDateString("en-GB", { month: "long" });
  const year = date.getFullYear();

  return `${day}${getOrdinalSuffix(day)} ${month} ${year}`;
};

// Utility function to convert period type to singular form
const getPeriodTypeSingular = (type: string): string => {
  switch (type.toLowerCase()) {
    case "weekly":
      return "Week";
    case "two_weekly":
      return "Two Week";
    case "four_weekly":
      return "Four Week";
    case "monthly":
      return "Month";
    case "quarterly":
      return "Quarter";
    case "yearly":
      return "Year";
    default:
      return type.charAt(0).toUpperCase() + type.slice(1);
  }
};

export interface PayslipEditorProps {
  periodId: string;
  employeeId: string | null;
  onBackToOverview?: () => void;
  onSwitchToBatch?: () => void;
  payPeriods: PayPeriod[];
}

const PayslipEditor: React.FC<PayslipEditorProps> = ({
  periodId,
  employeeId,
  onBackToOverview,
  onSwitchToBatch,
  payPeriods,
}) => {
  const [activeEmployee, setActiveEmployee] = useState<string | null>(
    employeeId,
  );
  const [payslip, setPayslip] = useState(mockPayslip);

  const employees = useEmployeesForPeriod(periodId);

  // Find the selected period and get its type using useMemo for better performance
  const selectedPeriod = useMemo(() => {
    return payPeriods.find((p) => p.id === periodId);
  }, [payPeriods, periodId]);

  // Get period type from selected period
  const periodType = useMemo(() => {
    if (selectedPeriod) {
      // Convert period type to proper case for display (e.g., "Weekly" instead of "weekly")
      return (
        selectedPeriod.type.charAt(0).toUpperCase() +
        selectedPeriod.type.slice(1)
      );
    }
    return "Monthly"; // Default fallback
  }, [selectedPeriod]);

  // Handle employee selection
  const handleEmployeeSelect = (id: string) => {
    setActiveEmployee(id);
    // In a real implementation, we would fetch the payslip data for this employee
  };

  // If no employee is selected, show the employee list
  if (!activeEmployee) {
    return (
      <div className="grid w-full grid-cols-1 gap-4">
        <Card className="p-4">
          <h2 className="mb-4 text-lg font-semibold">Select an Employee</h2>
          <EmployeeList
            employees={employees}
            periodId={periodId}
            onEmployeeSelect={handleEmployeeSelect}
          />
        </Card>
      </div>
    );
  }

  // Find the selected employee
  const selectedEmployee = employees.find((emp) => emp.id === activeEmployee);

  // Generate the payslip header text
  const getPayslipHeaderText = (): string => {
    if (!selectedEmployee || !selectedPeriod) {
      return `${selectedEmployee?.name || "Employee"} - Payslip for ${periodId.replace(/-/g, " ")}`;
    }

    const employeeName = selectedEmployee.name;
    const periodTypeSingular = getPeriodTypeSingular(selectedPeriod.type);
    const formattedEndDate = formatDateWithOrdinal(selectedPeriod.period_end);

    return `${employeeName} - ${periodTypeSingular} Ending ${formattedEndDate}`;
  };

  return (
    <PayslipReadOnlyProvider employeeId={activeEmployee} periodId={periodId}>
      <div className="mt-1 flex w-full gap-2">
        {/* Left sidebar - Employee list */}
        <div style={{ width: "235px", flexShrink: 0 }}>
          <Card className="p-1.5">
            <div className="mb-1 flex justify-center gap-1">
              <Button
                size="sm"
                variant="default"
                onClick={onBackToOverview}
                className=""
              >
                <LayoutGrid className="mr-1 size-4 text-white" />
                Overview
              </Button>
              <Button
                size="sm"
                variant="default"
                onClick={onSwitchToBatch}
                className=""
              >
                <Edit className="mr-1 size-4 text-white" />
                Batch Edit
              </Button>
            </div>
            <EmployeeList
              employees={employees}
              periodId={periodId}
              selectedEmployeeId={activeEmployee}
              onEmployeeSelect={handleEmployeeSelect}
            />
          </Card>
        </div>

        {/* Main content - Payslip editor */}
        <div className="flex-1">
          <Card className="flex flex-col overflow-hidden p-3">
            <div
              className="flex-grow overflow-y-auto pr-3"
              style={{
                height: `calc(96vh - ${localStorage.getItem(`${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_visible`) === "true" ? 300 : 150}px)`,
                minHeight: "300px",
              }}
            >
              <div className="flex items-center justify-between">
                <h2 className="text-foreground dark:text-foreground text-lg font-normal">
                  {getPayslipHeaderText()}
                </h2>
                <ReadOnlyIndicator />
              </div>

              <Separator className="my-4" />

              {/* Pay Section */}
              {activeEmployee && (
                <PayslipBasicSectionContainer
                  key={`basic-${activeEmployee}-${periodId}`}
                  employeeId={activeEmployee}
                  periodId={periodId}
                  periodType={periodType}
                />
              )}

              <Separator className="my-6" />

              {/* Additions Section */}
              {activeEmployee && (
                <PayslipAdditionsSection
                  key={`additions-${activeEmployee}-${periodId}`}
                  employeeId={activeEmployee}
                  periodId={periodId}
                />
              )}

              <Separator className="my-2" />

              {/* Deductions Section */}
              {activeEmployee && (
                <PayslipDeductionsSection
                  key={`deductions-${activeEmployee}-${periodId}`}
                  employeeId={activeEmployee}
                  periodId={periodId}
                />
              )}

              <Separator className="my-6" />

              {/* Statutory Pay Section */}
              {activeEmployee && (
                <PayslipStatutorySection
                  key={`statutory-${activeEmployee}-${periodId}`}
                  data={payslip.statutory}
                  onChange={(data) =>
                    setPayslip({ ...payslip, statutory: data })
                  }
                />
              )}

              <Separator className="my-6" />

              {/* Pension Section */}
              {activeEmployee && (
                <PayslipPensionSection
                  key={`pension-${activeEmployee}-${periodId}`}
                  data={payslip.pension}
                  onChange={(data) => setPayslip({ ...payslip, pension: data })}
                />
              )}

              <Separator className="my-6" />

              {/* Notes Section */}
              {activeEmployee && (
                <PayslipNotesSection
                  key={`notes-${activeEmployee}-${periodId}`}
                  data={payslip.notes}
                  onChange={(data) => setPayslip({ ...payslip, notes: data })}
                />
              )}
            </div>
          </Card>
        </div>

        {/* Right sidebar - Summary */}
        <div className="w-[250px] flex-shrink-0 px-1">
          <PayslipSummary
            calculations={payslip.calculations}
            ytd={payslip.ytd}
          />
        </div>
      </div>
    </PayslipReadOnlyProvider>
  );
};

export default PayslipEditor;
