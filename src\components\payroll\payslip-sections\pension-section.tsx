"use client";

import React from "react";
import { ReadOnlyAwareSectionHeader } from "@/components/payroll/ui/readonly-aware-components";

interface PensionData {
  employeeContribution: number;
  employerContribution: number;
  isPercentage: boolean;
  employeePercentage: number;
  employerPercentage: number;
}

interface PayslipPensionSectionProps {
  data: PensionData;
  onChange: (data: PensionData) => void;
}

const PayslipPensionSection: React.FC<PayslipPensionSectionProps> = ({
  data,
  onChange,
}) => {
  return (
    <div className="space-y-2">
      <ReadOnlyAwareSectionHeader title="Pension" />

      {/* Placeholder message for pension section */}
      <div className="rounded-lg bg-slate-50 p-4 text-center text-sm text-slate-500 dark:bg-zinc-800 dark:text-slate-400">
        Pension details will be fetched from pension scheme configuration
      </div>
    </div>
  );
};

export default PayslipPensionSection;
