"use client";

import React from "react";
import { Trash2 } from "lucide-react";
import {
  ReadOnlyAwareButton,
  ReadOnlyAwarePayslipNumberInput,
  ReadOnlyAwareRepeatingControl,
  ReadOnlyAwareZeroizeControl,
  ReadOnlyAwareSectionHeader,
  ReadOnlyAwareRateSelectorControl,
} from "@/components/payroll/ui/readonly-aware-components";
import type { Employee } from "@/lib/schemas/employee";

// Define interfaces for each pay element type
interface PayElement {
  id: string;
  isRepeating: boolean;
  zeroizeNext: boolean;
}

interface SalaryElement extends PayElement {
  type: "salary";
  amount: number;
  periodType: string;
}

interface DailyElement extends PayElement {
  type: "daily";
  rate: number;
  days: number;
  // Calculated amount (days * rate)
  amount?: number;
  // Track the source of the rate (selector or manual input)
  rateSource?: "selector" | "manual";
  // Store the rate label when selected from the selector
  rateLabel?: string;
}

interface HourlyElement extends PayElement {
  type: "hourly";
  rate: number;
  hours: number;
  // Calculated amount (hours * rate)
  amount?: number;
  // Track the source of the rate (selector or manual input)
  rateSource?: "selector" | "manual";
  // Store the rate label when selected from the selector
  rateLabel?: string;
}

export type BasicPayElement = SalaryElement | DailyElement | HourlyElement;

export interface BasicSectionData {
  elements: BasicPayElement[];
}

interface PayslipBasicSectionProps {
  data: BasicSectionData;
  onChange: (data: BasicSectionData) => void;
  periodType?: string; // Optional to maintain backward compatibility
  /** Employee record for rate defaults */
  employee?: Employee;
  /** Callback when user saves a new rate */
  onSaveRate?: (
    type: "daily" | "hourly",
    elementId: string,
    label: string,
    rate: number,
  ) => void;
}

export const PayslipBasicSection: React.FC<PayslipBasicSectionProps> = ({
  data,
  onChange,
  periodType = "Basic",
  employee,
  onSaveRate,
}) => {
  // If data.elements is undefined, initialize with an empty array
  const elements = data.elements || [];

  // Calculate amounts for all elements when component mounts
  React.useEffect(() => {
    // Only proceed if there are elements to process
    if (elements.length === 0) return;

    // Check if any elements need calculation updates
    let needsUpdate = false;

    const updatedElements = elements.map((element) => {
      if (element.type === "daily") {
        const dailyElement = element as DailyElement;
        const calculatedAmount = dailyElement.days * dailyElement.rate;

        // Check if calculation is needed
        if (
          dailyElement.amount === undefined ||
          Math.abs(dailyElement.amount - calculatedAmount) > 0.001
        ) {
          needsUpdate = true;
          return { ...dailyElement, amount: calculatedAmount };
        }
      } else if (element.type === "hourly") {
        const hourlyElement = element as HourlyElement;
        const calculatedAmount = hourlyElement.hours * hourlyElement.rate;

        // Check if calculation is needed
        if (
          hourlyElement.amount === undefined ||
          Math.abs(hourlyElement.amount - calculatedAmount) > 0.001
        ) {
          needsUpdate = true;
          return { ...hourlyElement, amount: calculatedAmount };
        }
      }
      return element;
    });

    // Only update if needed to prevent infinite loops
    if (needsUpdate) {
      onChange({ ...data, elements: updatedElements });
    }
    // Only run on mount and when elements array reference changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle input changes for a specific element
  const handleElementChange = (
    index: number,
    field: string,
    value: string | boolean | number,
  ) => {
    const newElements = [...elements];
    const element = { ...newElements[index] };

    // Type checking and conversion
    if (
      typeof value === "string" &&
      ["amount", "rate", "days", "hours"].includes(field)
    ) {
      (element as any)[field] = parseFloat(value) || 0;
    } else {
      (element as any)[field] = value;
    }

    // If isRepeating is set to false, also set zeroizeNext to false
    if (field === "isRepeating" && value === false) {
      element.zeroizeNext = false;
    }

    // Note: Amount calculation is now handled directly in the input handlers

    newElements[index] = element;
    onChange({ ...data, elements: newElements });
  };

  // Add a new salary element
  const addSalaryElement = () => {
    const newElement: SalaryElement = {
      id: `salary-${Date.now()}`,
      type: "salary",
      amount: 0,
      periodType: periodType, // Use the provided period type
      isRepeating: true,
      zeroizeNext: false,
    };

    // Sort elements to maintain order: Salary, Daily, Hourly
    const newElements = [...elements, newElement].sort((a, b) => {
      const order = { salary: 0, daily: 1, hourly: 2 };
      return order[a.type] - order[b.type];
    });

    onChange({ ...data, elements: newElements });
  };

  // Add a new daily element
  const addDailyElement = () => {
    const newElement = {
      id: `daily-${Date.now()}`,
      type: "daily",
      rate: 0,
      days: 0,
      amount: 0, // Initialize calculated amount
      isRepeating: true,
      zeroizeNext: false,
      rateSource: "manual" as DailyElement["rateSource"], // Default to manual for new elements
    } as DailyElement;

    // Sort elements to maintain order: Salary, Daily, Hourly
    const newElements = [...elements, newElement].sort((a, b) => {
      const order = { salary: 0, daily: 1, hourly: 2 };
      return order[a.type] - order[b.type];
    });

    onChange({ ...data, elements: newElements });
  };

  // Add a new hourly element
  const addHourlyElement = () => {
    const newElement = {
      id: `hourly-${Date.now()}`,
      type: "hourly",
      rate: 0,
      hours: 0,
      amount: 0, // Initialize calculated amount
      isRepeating: true,
      zeroizeNext: false,
      rateSource: "manual" as DailyElement["rateSource"], // Default to manual for new elements
    } as HourlyElement;

    // Sort elements to maintain order: Salary, Daily, Hourly
    const newElements = [...elements, newElement].sort((a, b) => {
      const order = { salary: 0, daily: 1, hourly: 2 };
      return order[a.type] - order[b.type];
    });

    onChange({ ...data, elements: newElements });
  };

  // Remove an element
  const removeElement = (id: string) => {
    const newElements = elements.filter((element) => element.id !== id);
    onChange({ ...data, elements: newElements });
  };

  // Clear all values (set to zero but keep elements)
  const clearAllValues = () => {
    const clearedElements = elements.map((element) => {
      if (element.type === "salary") {
        return { ...element, amount: 0 };
      } else if (element.type === "daily") {
        return { ...element, days: 0, rate: 0, amount: 0 };
      } else if (element.type === "hourly") {
        return { ...element, hours: 0, rate: 0, amount: 0 };
      }
      return element;
    });
    onChange({ ...data, elements: clearedElements });
  };

  // Remove all elements
  const removeAllElements = () => {
    onChange({ ...data, elements: [] });
  };

  // Group elements by type
  const salaryElements = elements.filter((e) => e.type === "salary");
  const dailyElements = elements.filter((e) => e.type === "daily");
  const hourlyElements = elements.filter((e) => e.type === "hourly");

  // derive base rates and saved rates from employee record
  const dailyBaseRate = employee?.dailyRates?.[0]?.rate ?? 0;
  const hourlyBaseRate = employee?.hourlyRates?.[0]?.rate ?? 0;
  const dailySavedRates =
    employee?.dailyRates?.map((r) => ({ label: r.name, value: r.rate })) ?? [];
  const hourlySavedRates =
    employee?.hourlyRates?.map((r) => ({ label: r.name, value: r.rate })) ?? [];

  return (
    <div>
      <ReadOnlyAwareSectionHeader
        title="Basic Pay"
        sectionType="basic"
        addButtons={[
          { label: "Salary", onClick: addSalaryElement },
          { label: "Daily", onClick: addDailyElement },
          { label: "Hourly", onClick: addHourlyElement },
        ]}
        actionButtons={[
          {
            label: "Clear Values",
            onClick: clearAllValues,
            variant: "outline",
          },
          {
            label: "Delete All",
            onClick: removeAllElements,
            variant: "destructive",
          },
        ]}
      />

      {/* Pay elements container */}
      <div className="min-h-[28px]">
        {/* Salary Elements */}
        <div className="space-y-1">
          {salaryElements.map((element) => {
            const elementIndex = elements.findIndex((e) => e.id === element.id);
            return (
              <div key={element.id} className="px-1.5">
                <div className="flex items-center">
                  <ReadOnlyAwareButton
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground mr-3 h-5 w-5"
                    onClick={() => removeElement(element.id)}
                  >
                    <Trash2 className="size-4" />
                  </ReadOnlyAwareButton>
                  <div className="flex-grow">
                    <div className="flex items-center">
                      <div className="ml-4 grid grid-cols-[148px_110px_98px_150px] items-center">
                        <span className="text-foreground pr-2 text-sm whitespace-nowrap">
                          {periodType} Pay
                        </span>
                        <ReadOnlyAwarePayslipNumberInput
                          id={`salary-${element.id}`}
                          className="h-7 w-full text-sm"
                          value={(element as SalaryElement).amount}
                          onChange={(value) =>
                            handleElementChange(
                              elementIndex,
                              "amount",
                              value ?? 0,
                            )
                          }
                          decimalPlaces={2}
                          allowCalculations={true}
                          currencySymbol=""
                        />
                        <span className="text-foreground pl-2 text-sm whitespace-nowrap"></span>
                        <div className="ml-4 flex items-center">
                          <ReadOnlyAwareRepeatingControl
                            id={element.id}
                            isChecked={element.isRepeating}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "isRepeating",
                                checked,
                              )
                            }
                          />

                          <ReadOnlyAwareZeroizeControl
                            id={element.id}
                            isChecked={element.zeroizeNext}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "zeroizeNext",
                                checked,
                              )
                            }
                            disabled={!element.isRepeating}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Daily Elements */}
        <div className="mt-3 mb-3 space-y-0">
          {dailyElements.map((element) => {
            const elementIndex = elements.findIndex((e) => e.id === element.id);
            return (
              <div key={element.id} className="bg-card rounded-sm px-1.5">
                <div className="flex items-center">
                  <ReadOnlyAwareButton
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground mr-3 h-5 w-5"
                    onClick={() => removeElement(element.id)}
                  >
                    <Trash2 className="size-4" />
                  </ReadOnlyAwareButton>
                  <div className="flex-grow">
                    <div className="mb-1 flex items-center">
                      <div className="ml-4 grid grid-cols-[75px_65px_140px_60px_150px] items-center gap-1">
                        <div className="relative">
                          <ReadOnlyAwarePayslipNumberInput
                            id={`days-worked-${element.id}`}
                            className="h-7 w-full text-sm"
                            value={(element as DailyElement).days}
                            onChange={(value) => {
                              // Calculate the new amount immediately
                              const newValue = value ?? 0;
                              const rate = (element as DailyElement).rate;
                              const calculatedAmount = newValue * rate;

                              // Update both days and amount in one operation
                              const updatedElement = {
                                ...(element as DailyElement),
                                days: newValue,
                                amount: calculatedAmount,
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              onChange({ ...data, elements: newElements });
                            }}
                            decimalPlaces={4}
                            allowCalculations={true}
                            onKeyDown={(e) => {
                              if (e.key === "Tab") return;
                              // Process calculation on every keystroke
                              setTimeout(() => {
                                const inputValue = (
                                  e.target as HTMLInputElement
                                ).value;
                                if (inputValue && inputValue !== "-") {
                                  try {
                                    // Try to parse the current input value
                                    const cleanedValue = inputValue.replace(
                                      /,/g,
                                      "",
                                    );
                                    const parsedValue =
                                      parseFloat(cleanedValue);

                                    if (!isNaN(parsedValue)) {
                                      // If it's a valid number, update and calculate
                                      const rate = (element as DailyElement)
                                        .rate;
                                      const calculatedAmount =
                                        parsedValue * rate;

                                      const updatedElement = {
                                        ...(element as DailyElement),
                                        days: parsedValue,
                                        amount: calculatedAmount,
                                      };

                                      const newElements = [...elements];
                                      newElements[elementIndex] =
                                        updatedElement;
                                      onChange({
                                        ...data,
                                        elements: newElements,
                                      });
                                    }
                                  } catch (error) {
                                    // Ignore parsing errors during typing
                                  }
                                }
                              }, 0);
                            }}
                          />
                        </div>
                        <span className="px-2 text-sm whitespace-nowrap text-slate-600 dark:text-slate-300">
                          Days @
                        </span>
                        <div className="flex items-center">
                          <ReadOnlyAwarePayslipNumberInput
                            id={`daily-rate-${element.id}`}
                            className="h-7 w-full min-w-[110px] text-sm"
                            value={(element as DailyElement).rate}
                            onChange={(value) => {
                              // Calculate the new amount immediately
                              const newValue = value ?? 0;
                              const days = (element as DailyElement).days;
                              const calculatedAmount = days * newValue;

                              // Update both rate and amount in one operation
                              const updatedElement = {
                                ...(element as DailyElement),
                                rate: newValue,
                                amount: calculatedAmount,
                                // Mark this as a manual rate entry
                                rateSource:
                                  "manual" as DailyElement["rateSource"],
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              onChange({ ...data, elements: newElements });
                            }}
                            decimalPlaces={2}
                            allowCalculations={true}
                            currencySymbol="£"
                            onKeyDown={(e) => {
                              if (e.key === "Tab") return;
                              // Process calculation on every keystroke
                              setTimeout(() => {
                                const inputValue = (
                                  e.target as HTMLInputElement
                                ).value;
                                if (inputValue && inputValue !== "-") {
                                  try {
                                    // Try to parse the current input value
                                    const cleanedValue = inputValue.replace(
                                      /,/g,
                                      "",
                                    );
                                    const parsedValue =
                                      parseFloat(cleanedValue);

                                    if (!isNaN(parsedValue)) {
                                      // If it's a valid number, update and calculate
                                      const days = (element as DailyElement)
                                        .days;
                                      const calculatedAmount =
                                        days * parsedValue;

                                      const updatedElement = {
                                        ...(element as DailyElement),
                                        rate: parsedValue,
                                        amount: calculatedAmount,
                                        // Mark this as a manual rate entry
                                        rateSource:
                                          "manual" as DailyElement["rateSource"],
                                      };

                                      const newElements = [...elements];
                                      newElements[elementIndex] =
                                        updatedElement;
                                      onChange({
                                        ...data,
                                        elements: newElements,
                                      });
                                    }
                                  } catch (error) {
                                    // Ignore parsing errors during typing
                                  }
                                }
                              }, 0);
                            }}
                          />
                          <ReadOnlyAwareRateSelectorControl
                            id={element.id}
                            currentRate={(element as DailyElement).rate}
                            standardRate={dailyBaseRate}
                            savedRates={dailySavedRates}
                            onSaveRate={(label, rate) =>
                              onSaveRate?.("daily", element.id, label, rate)
                            }
                            onChange={(rate, label) => {
                              // Calculate the new amount immediately
                              const days = (element as DailyElement).days;
                              const calculatedAmount = days * rate;

                              // Update both rate and amount in one operation
                              const updatedElement = {
                                ...(element as DailyElement),
                                rate: rate,
                                amount: calculatedAmount,
                                // Mark this as a selector rate entry
                                rateSource: "selector" as const,
                                // Store the rate label based on what was selected
                                rateLabel: label,
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              onChange({ ...data, elements: newElements });
                            }}
                          />
                        </div>
                        {/* Rate indicator */}
                        <span className="ml-1 text-xs font-normal whitespace-nowrap text-slate-600 dark:text-slate-300">
                          {(element as DailyElement).rate > 0 &&
                            ((element as DailyElement).rateSource === "manual"
                              ? "Custom"
                              : (element as DailyElement).rateLabel || "")}
                        </span>
                        <div className="ml-4 flex items-center">
                          <ReadOnlyAwareRepeatingControl
                            id={element.id}
                            isChecked={element.isRepeating}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "isRepeating",
                                checked,
                              )
                            }
                          />

                          <ReadOnlyAwareZeroizeControl
                            id={element.id}
                            isChecked={element.zeroizeNext}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "zeroizeNext",
                                checked,
                              )
                            }
                            disabled={!element.isRepeating}
                          />

                          {/* Only show amount if it's not zero */}
                          {(element as DailyElement).amount !== 0 &&
                            (element as DailyElement).amount !== undefined && (
                              <span className="ml-8 text-sm font-normal text-sky-700 dark:text-slate-300">
                                £
                                {Number(
                                  (element as DailyElement).amount,
                                ).toLocaleString("en-GB", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                              </span>
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Hourly Elements */}
        <div className="mb-1">
          {hourlyElements.map((element) => {
            const elementIndex = elements.findIndex((e) => e.id === element.id);
            return (
              <div key={element.id} className="bg-card rounded-sm px-1.5">
                <div className="flex items-center">
                  <ReadOnlyAwareButton
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground mr-3 h-5 w-5"
                    onClick={() => removeElement(element.id)}
                  >
                    <Trash2 className="size-4" />
                  </ReadOnlyAwareButton>
                  <div className="flex-grow">
                    <div className="mb-1 flex items-center">
                      <div className="ml-4 grid grid-cols-[75px_65px_140px_60px_150px] items-center gap-1">
                        <div className="relative">
                          <ReadOnlyAwarePayslipNumberInput
                            id={`hours-worked-${element.id}`}
                            className="h-7 w-full text-sm"
                            value={(element as HourlyElement).hours}
                            onChange={(value) => {
                              // Calculate the new amount immediately
                              const newValue = value ?? 0;
                              const rate = (element as HourlyElement).rate;
                              const calculatedAmount = newValue * rate;

                              // Update both hours and amount in one operation
                              const updatedElement = {
                                ...(element as HourlyElement),
                                hours: newValue,
                                amount: calculatedAmount,
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              onChange({ ...data, elements: newElements });
                            }}
                            decimalPlaces={4}
                            allowCalculations={true}
                            onKeyDown={(e) => {
                              if (e.key === "Tab") return;
                              // Process calculation on every keystroke
                              setTimeout(() => {
                                const inputValue = (
                                  e.target as HTMLInputElement
                                ).value;
                                if (inputValue && inputValue !== "-") {
                                  try {
                                    // Try to parse the current input value
                                    const cleanedValue = inputValue.replace(
                                      /,/g,
                                      "",
                                    );
                                    const parsedValue =
                                      parseFloat(cleanedValue);

                                    if (!isNaN(parsedValue)) {
                                      // If it's a valid number, update and calculate
                                      const rate = (element as HourlyElement)
                                        .rate;
                                      const calculatedAmount =
                                        parsedValue * rate;

                                      const updatedElement = {
                                        ...(element as HourlyElement),
                                        hours: parsedValue,
                                        amount: calculatedAmount,
                                      };

                                      const newElements = [...elements];
                                      newElements[elementIndex] =
                                        updatedElement;
                                      onChange({
                                        ...data,
                                        elements: newElements,
                                      });
                                    }
                                  } catch (error) {
                                    // Ignore parsing errors during typing
                                  }
                                }
                              }, 0);
                            }}
                          />
                        </div>
                        <span className="px-2 text-sm whitespace-nowrap text-slate-600 dark:text-slate-300">
                          Hours @
                        </span>
                        <div className="flex items-center">
                          <ReadOnlyAwarePayslipNumberInput
                            id={`hourly-rate-${element.id}`}
                            className="h-7 w-full min-w-[110px] text-sm"
                            value={(element as HourlyElement).rate}
                            onChange={(value) => {
                              // Calculate the new amount immediately
                              const newValue = value ?? 0;
                              const hours = (element as HourlyElement).hours;
                              const calculatedAmount = hours * newValue;

                              // Update both rate and amount in one operation
                              const updatedElement = {
                                ...(element as HourlyElement),
                                rate: newValue,
                                amount: calculatedAmount,
                                // Mark this as a manual rate entry
                                rateSource:
                                  "manual" as DailyElement["rateSource"],
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              onChange({ ...data, elements: newElements });
                            }}
                            decimalPlaces={2}
                            allowCalculations={true}
                            currencySymbol="£"
                            onKeyDown={(e) => {
                              if (e.key === "Tab") return;
                              // Process calculation on every keystroke
                              setTimeout(() => {
                                const inputValue = (
                                  e.target as HTMLInputElement
                                ).value;
                                if (inputValue && inputValue !== "-") {
                                  try {
                                    // Try to parse the current input value
                                    const cleanedValue = inputValue.replace(
                                      /,/g,
                                      "",
                                    );
                                    const parsedValue =
                                      parseFloat(cleanedValue);

                                    if (!isNaN(parsedValue)) {
                                      // If it's a valid number, update and calculate
                                      const hours = (element as HourlyElement)
                                        .hours;
                                      const calculatedAmount =
                                        hours * parsedValue;

                                      const updatedElement = {
                                        ...(element as HourlyElement),
                                        rate: parsedValue,
                                        amount: calculatedAmount,
                                        // Mark this as a manual rate entry
                                        rateSource:
                                          "manual" as DailyElement["rateSource"],
                                      };

                                      const newElements = [...elements];
                                      newElements[elementIndex] =
                                        updatedElement;
                                      onChange({
                                        ...data,
                                        elements: newElements,
                                      });
                                    }
                                  } catch (error) {
                                    // Ignore parsing errors during typing
                                  }
                                }
                              }, 0);
                            }}
                          />
                          <ReadOnlyAwareRateSelectorControl
                            id={element.id}
                            currentRate={(element as HourlyElement).rate}
                            standardRate={hourlyBaseRate}
                            savedRates={hourlySavedRates}
                            onSaveRate={(label, rate) =>
                              onSaveRate?.("hourly", element.id, label, rate)
                            }
                            onChange={(rate, label) => {
                              // Calculate the new amount immediately
                              const hours = (element as HourlyElement).hours;
                              const calculatedAmount = hours * rate;

                              // Update both rate and amount in one operation
                              const updatedElement = {
                                ...(element as HourlyElement),
                                rate: rate,
                                amount: calculatedAmount,
                                // Mark this as a selector rate entry
                                rateSource: "selector" as const,
                                // Store the rate label based on what was selected
                                rateLabel: label,
                              };

                              const newElements = [...elements];
                              newElements[elementIndex] = updatedElement;
                              onChange({ ...data, elements: newElements });
                            }}
                          />
                        </div>
                        {/* Rate indicator */}
                        <span className="ml-1 text-xs font-normal whitespace-nowrap text-slate-600 dark:text-slate-300">
                          {(element as HourlyElement).rate > 0 &&
                            ((element as HourlyElement).rateSource === "manual"
                              ? "Custom"
                              : (element as HourlyElement).rateLabel || "")}
                        </span>
                        <div className="ml-4 flex items-center">
                          <ReadOnlyAwareRepeatingControl
                            id={element.id}
                            isChecked={element.isRepeating}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "isRepeating",
                                checked,
                              )
                            }
                          />

                          <ReadOnlyAwareZeroizeControl
                            id={element.id}
                            isChecked={element.zeroizeNext}
                            onChange={(checked) =>
                              handleElementChange(
                                elementIndex,
                                "zeroizeNext",
                                checked,
                              )
                            }
                            disabled={!element.isRepeating}
                          />

                          {/* Only show amount if it's not zero */}
                          {(element as HourlyElement).amount !== 0 &&
                            (element as HourlyElement).amount !== undefined && (
                              <span className="ml-8 text-sm font-normal text-sky-700 dark:text-slate-300">
                                £
                                {Number(
                                  (element as HourlyElement).amount,
                                ).toLocaleString("en-GB", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                              </span>
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
