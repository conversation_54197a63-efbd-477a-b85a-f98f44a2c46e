"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { NumberInput } from "@/components/ui/number-input";
import { PayslipNumberInput } from "@/components/payroll/payslip-controls/payslip-number-input";
import { AddElementButton } from "@/components/payroll/payslip-controls/add-element-button";
import { RepeatingControl } from "@/components/payroll/payslip-controls/repeating-control";
import { ZeroizeControl } from "@/components/payroll/payslip-controls/zeroize-control";
import { ShowOnPayslipControl } from "@/components/payroll/payslip-controls/show-on-payslip-control";
import { SectionHeader } from "@/components/payroll/payslip-controls/section-header";
import { RateSelectorControl } from "@/components/payroll/payslip-controls/rate-selector-control";
import { useIsPayslipReadOnly } from "@/components/payroll/contexts/payslip-readonly-context";
import { cn } from "@/lib/utils";

// Enhanced Button that respects read-only context
interface ReadOnlyAwareButtonProps extends React.ComponentProps<typeof Button> {
  ignoreReadOnly?: boolean; // Allow override for navigation buttons
}

export const ReadOnlyAwareButton: React.FC<ReadOnlyAwareButtonProps> = ({
  disabled,
  ignoreReadOnly = false,
  className,
  ...props
}) => {
  const isReadOnly = useIsPayslipReadOnly();
  const shouldDisable = ignoreReadOnly ? disabled : disabled || isReadOnly;

  return (
    <Button
      disabled={shouldDisable}
      className={cn(
        shouldDisable && isReadOnly && "cursor-not-allowed opacity-60",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced Input that respects read-only context
interface ReadOnlyAwareInputProps extends React.ComponentProps<typeof Input> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareInput: React.FC<ReadOnlyAwareInputProps> = ({
  disabled,
  ignoreReadOnly = false,
  className,
  ...props
}) => {
  const isReadOnly = useIsPayslipReadOnly();
  const shouldDisable = ignoreReadOnly ? disabled : disabled || isReadOnly;

  return (
    <Input
      disabled={shouldDisable}
      className={cn(
        shouldDisable &&
          isReadOnly &&
          "bg-muted text-muted-foreground cursor-not-allowed",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced Checkbox that respects read-only context
interface ReadOnlyAwareCheckboxProps
  extends React.ComponentProps<typeof Checkbox> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareCheckbox: React.FC<ReadOnlyAwareCheckboxProps> = ({
  disabled,
  ignoreReadOnly = false,
  className,
  ...props
}) => {
  const isReadOnly = useIsPayslipReadOnly();
  const shouldDisable = ignoreReadOnly ? disabled : disabled || isReadOnly;

  return (
    <Checkbox
      disabled={shouldDisable}
      className={cn(
        shouldDisable && isReadOnly && "cursor-not-allowed opacity-60",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced Switch that respects read-only context
interface ReadOnlyAwareSwitchProps extends React.ComponentProps<typeof Switch> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareSwitch: React.FC<ReadOnlyAwareSwitchProps> = ({
  disabled,
  ignoreReadOnly = false,
  className,
  ...props
}) => {
  const isReadOnly = useIsPayslipReadOnly();
  const shouldDisable = ignoreReadOnly ? disabled : disabled || isReadOnly;

  return (
    <Switch
      disabled={shouldDisable}
      className={cn(
        shouldDisable && isReadOnly && "cursor-not-allowed opacity-60",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced Textarea that respects read-only context
interface ReadOnlyAwareTextareaProps
  extends React.ComponentProps<typeof Textarea> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareTextarea: React.FC<ReadOnlyAwareTextareaProps> = ({
  disabled,
  ignoreReadOnly = false,
  className,
  ...props
}) => {
  const isReadOnly = useIsPayslipReadOnly();
  const shouldDisable = ignoreReadOnly ? disabled : disabled || isReadOnly;

  return (
    <Textarea
      disabled={shouldDisable}
      className={cn(
        shouldDisable &&
          isReadOnly &&
          "bg-muted text-muted-foreground cursor-not-allowed",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced NumberInput that respects read-only context
interface ReadOnlyAwareNumberInputProps
  extends React.ComponentProps<typeof NumberInput> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareNumberInput: React.FC<
  ReadOnlyAwareNumberInputProps
> = ({ disabled, ignoreReadOnly = false, className, ...props }) => {
  const isReadOnly = useIsPayslipReadOnly();
  const shouldDisable = ignoreReadOnly ? disabled : disabled || isReadOnly;

  return (
    <NumberInput
      disabled={shouldDisable}
      className={cn(
        shouldDisable &&
          isReadOnly &&
          "bg-muted text-muted-foreground cursor-not-allowed",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced PayslipNumberInput that respects read-only context
interface ReadOnlyAwarePayslipNumberInputProps
  extends React.ComponentProps<typeof PayslipNumberInput> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwarePayslipNumberInput: React.FC<
  ReadOnlyAwarePayslipNumberInputProps
> = ({ disabled, ignoreReadOnly = false, className, ...props }) => {
  const isReadOnly = useIsPayslipReadOnly();
  const shouldDisable = ignoreReadOnly ? disabled : disabled || isReadOnly;

  return (
    <PayslipNumberInput
      disabled={shouldDisable}
      className={cn(
        shouldDisable &&
          isReadOnly &&
          "bg-muted text-muted-foreground cursor-not-allowed",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced AddElementButton that respects read-only context
interface ReadOnlyAwareAddElementButtonProps
  extends React.ComponentProps<typeof AddElementButton> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareAddElementButton: React.FC<
  ReadOnlyAwareAddElementButtonProps
> = ({ onClick, ignoreReadOnly = false, className, ...props }) => {
  const isReadOnly = useIsPayslipReadOnly();

  const handleClick = () => {
    if (!ignoreReadOnly && isReadOnly) return;
    onClick();
  };

  return (
    <AddElementButton
      onClick={handleClick}
      className={cn(
        isReadOnly &&
          !ignoreReadOnly &&
          "pointer-events-none cursor-not-allowed opacity-60",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced RepeatingControl that respects read-only context
interface ReadOnlyAwareRepeatingControlProps
  extends React.ComponentProps<typeof RepeatingControl> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareRepeatingControl: React.FC<
  ReadOnlyAwareRepeatingControlProps
> = ({ onChange, ignoreReadOnly = false, className, ...props }) => {
  const isReadOnly = useIsPayslipReadOnly();

  const handleChange = (checked: boolean) => {
    if (!ignoreReadOnly && isReadOnly) return;
    onChange(checked);
  };

  return (
    <RepeatingControl
      onChange={handleChange}
      className={cn(
        isReadOnly &&
          !ignoreReadOnly &&
          "pointer-events-none cursor-not-allowed opacity-60",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced ZeroizeControl that respects read-only context
interface ReadOnlyAwareZeroizeControlProps
  extends React.ComponentProps<typeof ZeroizeControl> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareZeroizeControl: React.FC<
  ReadOnlyAwareZeroizeControlProps
> = ({ onChange, disabled, ignoreReadOnly = false, className, ...props }) => {
  const isReadOnly = useIsPayslipReadOnly();
  const shouldDisable = ignoreReadOnly ? disabled : disabled || isReadOnly;

  const handleChange = (checked: boolean) => {
    if (!ignoreReadOnly && isReadOnly) return;
    onChange(checked);
  };

  return (
    <ZeroizeControl
      onChange={handleChange}
      disabled={shouldDisable}
      className={cn(
        isReadOnly && !ignoreReadOnly && "cursor-not-allowed opacity-60",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced ShowOnPayslipControl that respects read-only context
interface ReadOnlyAwareShowOnPayslipControlProps
  extends React.ComponentProps<typeof ShowOnPayslipControl> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareShowOnPayslipControl: React.FC<
  ReadOnlyAwareShowOnPayslipControlProps
> = ({ onChange, disabled, ignoreReadOnly = false, className, ...props }) => {
  const isReadOnly = useIsPayslipReadOnly();
  const shouldDisable = ignoreReadOnly ? disabled : disabled || isReadOnly;

  const handleChange = (checked: boolean) => {
    if (!ignoreReadOnly && isReadOnly) return;
    onChange(checked);
  };

  return (
    <ShowOnPayslipControl
      onChange={handleChange}
      disabled={shouldDisable}
      className={cn(
        isReadOnly && !ignoreReadOnly && "cursor-not-allowed opacity-60",
        className,
      )}
      {...props}
    />
  );
};

// Enhanced RateSelectorControl that respects read-only context
interface ReadOnlyAwareRateSelectorControlProps
  extends React.ComponentProps<typeof RateSelectorControl> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareRateSelectorControl: React.FC<
  ReadOnlyAwareRateSelectorControlProps
> = ({ onChange, onSaveRate, ignoreReadOnly = false, className, ...props }) => {
  const isReadOnly = useIsPayslipReadOnly();

  const handleChange = (rate: number, label?: string) => {
    if (!ignoreReadOnly && isReadOnly) return;
    onChange(rate, label);
  };

  const handleSaveRate = (label: string, value: number) => {
    if (!ignoreReadOnly && isReadOnly) return;
    onSaveRate?.(label, value);
  };

  return (
    <div
      className={cn(
        isReadOnly &&
          !ignoreReadOnly &&
          "pointer-events-none cursor-not-allowed opacity-60",
      )}
    >
      <RateSelectorControl
        onChange={handleChange}
        onSaveRate={handleSaveRate}
        className={cn(
          isReadOnly && !ignoreReadOnly && "cursor-not-allowed",
          className,
        )}
        {...props}
      />
    </div>
  );
};

// Enhanced SectionHeader that respects read-only context
interface ReadOnlyAwareSectionHeaderProps
  extends React.ComponentProps<typeof SectionHeader> {
  ignoreReadOnly?: boolean;
}

export const ReadOnlyAwareSectionHeader: React.FC<
  ReadOnlyAwareSectionHeaderProps
> = ({
  addButtons = [],
  actionButtons = [],
  ignoreReadOnly = false,
  ...props
}) => {
  const isReadOnly = useIsPayslipReadOnly();

  // Disable add buttons if in read-only mode
  const enhancedAddButtons = addButtons.map((button) => ({
    ...button,
    onClick: () => {
      if (!ignoreReadOnly && isReadOnly) return;
      button.onClick();
    },
  }));

  // Disable action buttons if in read-only mode
  const enhancedActionButtons = actionButtons.map((button) => ({
    ...button,
    onClick: () => {
      if (!ignoreReadOnly && isReadOnly) return;
      button.onClick();
    },
  }));

  return (
    <div className={cn(isReadOnly && !ignoreReadOnly && "opacity-60")}>
      <SectionHeader
        addButtons={enhancedAddButtons}
        actionButtons={enhancedActionButtons}
        {...props}
      />
    </div>
  );
};

// Read-only indicator component
export const ReadOnlyIndicator: React.FC<{ className?: string }> = ({
  className,
}) => {
  const isReadOnly = useIsPayslipReadOnly();

  if (!isReadOnly) return null;

  return (
    <div
      className={cn(
        "text-muted-foreground bg-muted rounded-md border px-2 py-1 text-xs",
        className,
      )}
    >
      🔒 This payslip is finalized and cannot be edited
    </div>
  );
};
