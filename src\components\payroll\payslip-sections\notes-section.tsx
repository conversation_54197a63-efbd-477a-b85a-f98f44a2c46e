"use client";

import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Plus, Trash2 } from "lucide-react";
import {
  ReadOnlyAwareButton,
  ReadOnlyAwareTextarea,
  ReadOnlyAwareSectionHeader,
  ReadOnlyAwareRepeatingControl,
  ReadOnlyAwareShowOnPayslipControl,
} from "@/components/payroll/ui/readonly-aware-components";

interface NoteItem {
  id: string;
  content: string;
  showOnPayslip: boolean;
  isRepeating: boolean;
}

interface PayslipNotesSectionProps {
  data: NoteItem[];
  onChange: (data: NoteItem[]) => void;
}

const PayslipNotesSection: React.FC<PayslipNotesSectionProps> = ({
  data,
  onChange,
}) => {
  // Handle input changes
  const handleInputChange = (
    id: string,
    field: keyof NoteItem,
    value: string | boolean,
  ) => {
    const newData = [...data];
    const index = newData.findIndex((item) => item.id === id);

    if (index !== -1) {
      newData[index] = { ...newData[index], [field]: value };
      onChange(newData);
    }
  };

  // Add a new note
  const handleAddNote = () => {
    const newNote: NoteItem = {
      id: `note-${Date.now()}`,
      content: "",
      showOnPayslip: true,
      isRepeating: false,
    };

    onChange([...data, newNote]);
  };

  // Remove a note
  const handleRemoveNote = (id: string) => {
    onChange(data.filter((item) => item.id !== id));
  };

  return (
    <div className="space-y-2">
      <ReadOnlyAwareSectionHeader
        title="Notes"
        addButtons={[{ label: "Add Note", onClick: handleAddNote }]}
      />

      {data.length === 0 ? (
        <div className="text-muted-foreground p-2 text-center text-xs">
          No notes added. Click &quot;Add Note&quot; to create a new note.
        </div>
      ) : (
        data.map((note) => (
          <div key={note.id} className="bg-card mb-1 rounded-lg p-2">
            <div className="flex items-start justify-between">
              <div className="w-full space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Label
                      htmlFor={`note-${note.id}`}
                      className="text-xs font-medium"
                    >
                      Note
                    </Label>
                    <ReadOnlyAwareShowOnPayslipControl
                      id={note.id}
                      isChecked={note.showOnPayslip}
                      onChange={(checked) =>
                        handleInputChange(note.id, "showOnPayslip", checked)
                      }
                    />
                    <ReadOnlyAwareRepeatingControl
                      id={note.id}
                      isChecked={note.isRepeating}
                      onChange={(checked) =>
                        handleInputChange(note.id, "isRepeating", checked)
                      }
                    />
                  </div>
                  <ReadOnlyAwareButton
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => handleRemoveNote(note.id)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </ReadOnlyAwareButton>
                </div>

                <ReadOnlyAwareTextarea
                  id={`note-${note.id}`}
                  value={note.content}
                  onChange={(e) =>
                    handleInputChange(note.id, "content", e.target.value)
                  }
                  placeholder="Enter note text here..."
                  className="min-h-[60px] text-xs"
                />
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default PayslipNotesSection;
